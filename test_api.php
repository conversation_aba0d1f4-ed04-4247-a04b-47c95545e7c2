<?php

// Simple test script to verify the API endpoint
$url = 'http://127.0.0.1:8000/api/users';
$headers = [
    'Application-Token: test-token-123',
    'Content-Type: application/json',
    'Accept: application/json'
];

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

echo "Testing API endpoint: $url\n";
echo "With headers: " . implode(', ', $headers) . "\n\n";

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

if (curl_error($ch)) {
    echo "cURL Error: " . curl_error($ch) . "\n";
} else {
    echo "HTTP Status Code: $httpCode\n";
    echo "Response: $response\n";
}

curl_close($ch);

// Test without token
echo "\n\n--- Testing without token ---\n";
$headers_no_token = [
    'Content-Type: application/json',
    'Accept: application/json'
];

$ch2 = curl_init();
curl_setopt($ch2, CURLOPT_URL, $url);
curl_setopt($ch2, CURLOPT_HTTPHEADER, $headers_no_token);
curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch2, CURLOPT_TIMEOUT, 30);

$response2 = curl_exec($ch2);
$httpCode2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);

if (curl_error($ch2)) {
    echo "cURL Error: " . curl_error($ch2) . "\n";
} else {
    echo "HTTP Status Code: $httpCode2\n";
    echo "Response: $response2\n";
}

curl_close($ch2);
